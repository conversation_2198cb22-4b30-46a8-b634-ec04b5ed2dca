<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 简化的日志格式，只显示时间、级别和消息 -->
            <pattern>%d{HH:mm:ss.SSS} [%level] %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 设置Spring框架的日志级别为WARN，过滤掉DEBUG和INFO -->
    <logger name="org.springframework" level="WARN" />
    <logger name="org.springframework.web" level="WARN" />
    <logger name="org.springframework.web.servlet" level="WARN" />
    <logger name="org.springframework.web.servlet.mvc" level="WARN" />
    <logger name="org.springframework.web.servlet.DispatcherServlet" level="WARN" />
    
    <!-- 设置Hibernate日志级别 -->
    <logger name="org.hibernate" level="WARN" />
    
    <!-- 设置Tomcat日志级别 -->
    <logger name="org.apache.catalina" level="WARN" />
    <logger name="org.apache.coyote" level="WARN" />
    <logger name="org.apache.tomcat" level="WARN" />
    
    <!-- 我们的业务日志保持INFO级别 -->
    <logger name="com.conch.server" level="INFO" />
    
    <!-- 根日志级别设置为INFO -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
