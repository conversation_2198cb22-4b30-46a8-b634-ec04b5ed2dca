# 小田螺客户端开发调试命令手册

## 📱 ADB 相关命令

| 命令 | 功能说明 | 使用场景 |
|------|----------|----------|
| `adb shell "dumpsys activity activities \| grep -A 5 -B 5 conch"` | 查看应用活动状态，显示conch相关的Activity信息 | 检查应用是否正在运行、是否全屏等状态信息 |
| `adb shell am start -n com.conch.client/.MainActivity` | 启动小田螺客户端的主Activity | 远程启动应用，测试应用启动是否正常 |
| `adb install -r "conch-android\build\outputs\apk\debug\conch-android-debug.apk"` | 安装或重新安装APK到设备 | 部署新版本APK到测试设备 |
| `adb logcat -s AndroidRuntime:E` | 查看Android运行时错误日志 | 调试应用崩溃问题，查看异常堆栈信息 |
| `adb logcat -c` | 清空logcat日志缓存 | 清理旧日志，便于查看新的日志输出 |
| `adb logcat \| Select-String "com.conch.client"` | 过滤显示包含应用包名的日志 | 只查看与小田螺客户端相关的日志信息 |

## 🔨 Gradle 构建命令

| 命令 | 功能说明 | 使用场景 |
|------|----------|----------|
| `./gradlew :conch-android:assembleDebug` | 编译构建Debug版本的APK | 开发阶段构建测试版本 |
| `./gradlew clean` | 清理构建缓存和输出文件 | 解决构建问题，重新开始干净的构建 |

## 💻 PowerShell 系统命令

| 命令 | 功能说明 | 使用场景 |
|------|----------|----------|
| `Get-ChildItem "conch-android\build\outputs\apk\debug\conch-android-debug.apk" \| Select-Object Name, Length` | 查看APK文件信息（名称和大小） | 验证APK是否生成成功，检查文件大小 |
| `$env:JAVA_HOME="C:\Program Files\Android\Android Studio\jbr"` | 设置Java环境变量 | 解决Gradle构建时的Java路径问题 |

## 🚀 常用开发流程

### 1. 完整构建和部署流程
```bash
# 1. 清理项目
./gradlew clean

# 2. 构建APK
./gradlew :conch-android:assembleDebug

# 3. 安装到设备
adb install -r "conch-android\build\outputs\apk\debug\conch-android-debug.apk"

# 4. 启动应用
adb shell am start -n com.conch.client/.MainActivity
```

### 2. 调试问题流程
```bash
# 1. 清空日志
adb logcat -c

# 2. 启动应用
adb shell am start -n com.conch.client/.MainActivity

# 3. 查看错误日志
adb logcat -s AndroidRuntime:E

# 4. 查看应用特定日志
adb logcat | Select-String "com.conch.client"
```

### 3. 检查应用状态
```bash
# 查看应用运行状态
adb shell "dumpsys activity activities | grep -A 5 -B 5 conch"

# 查看APK文件信息
Get-ChildItem "conch-android\build\outputs\apk\debug\conch-android-debug.apk" | Select-Object Name, Length
```

## 📝 注意事项

1. **设备连接**：执行ADB命令前确保设备已连接并开启USB调试
2. **权限问题**：某些命令可能需要管理员权限
3. **路径问题**：命令中的路径需要根据实际项目结构调整
4. **Java环境**：如果遇到Java相关错误，设置正确的JAVA_HOME环境变量

## 🔧 故障排除

- **ADB设备未找到**：检查USB连接和驱动程序
- **构建失败**：尝试`./gradlew clean`后重新构建
- **安装失败**：使用`-r`参数强制重新安装
- **应用崩溃**：查看`adb logcat -s AndroidRuntime:E`获取详细错误信息
