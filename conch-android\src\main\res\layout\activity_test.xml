<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <Button
                android:id="@+id/btn_test"
                android:layout_width="wrap_content"
                android:text="测试按钮"
                android:layout_height="wrap_content"/>

            <EditText
                android:id="@+id/et_input"
                android:layout_width="match_parent"
                android:text="测试输入框"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="match_parent"
                android:text="测试列表1"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表2"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表3"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表4"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表5"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表6"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表7"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表8"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表9"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表0"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表11"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表12"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表13"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表14"
                android:layout_height="100dp"/>
            <TextView
                android:layout_width="match_parent"
                android:text="测试列表15"
                android:layout_height="100dp"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>