# 小田螺助手 - 部署和测试指南

## 🚀 快速开始

### 环境要求

#### 开发环境
- **JDK**: Java 17 或更高版本
- **Android Studio**: 最新版本 (支持Compose)
- **Gradle**: 8.0+ (项目已包含wrapper)
- **Android SDK**: API 24+ (Android 7.0+)

#### 运行环境
- **服务端**: 任何支持Java 17的系统
- **客户端**: Android 7.0+ 设备或模拟器

### 📦 构建项目

#### 1. 构建服务端
```bash
# 在项目根目录执行
./gradlew :conch-server:build

# 构建成功后会显示：BUILD SUCCESSFUL
```

#### 2. 构建客户端
```bash
# 构建Debug版本APK
./gradlew :conch-android:assembleDebug

# APK文件位置：conch-android/build/outputs/apk/debug/
```

### 🖥️ 启动服务端

#### 方法1: 使用Gradle (推荐开发环境)
```bash
# 确保已设置JAVA_HOME环境变量
./gradlew :conch-server:bootRun
```

#### 方法2: 使用JAR文件
```bash
# 先构建JAR
./gradlew :conch-server:bootJar

# 运行JAR
java -jar conch-server/build/libs/conch-server-1.0.0.jar
```

#### 验证服务端启动
访问: http://localhost:8080/api/v1/health

预期响应:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T12:00:00",
  "version": "1.0.0"
}
```

### 📱 安装客户端

#### 使用ADB安装
```bash
# 连接Android设备或启动模拟器
adb devices

# 安装APK
adb install conch-android/build/outputs/apk/debug/conch-android-debug.apk
```

#### 手动安装
1. 将APK文件传输到Android设备
2. 在设备上启用"未知来源"安装
3. 点击APK文件进行安装

## 🧪 功能测试

### 1. 基础API测试

#### 健康检查
```bash
curl http://localhost:8080/api/v1/health
```

#### 提交指令测试
```bash
curl -X POST http://localhost:8080/api/v1/voice/command \
  -H "Content-Type: application/json" \
  -d '{
    "textCommand": {
      "text": "打开微信",
      "confidence": 1.0,
      "timestamp": 1640995200000
    },
    "deviceInfo": {
      "model": "Test Device",
      "androidVersion": "Android 12",
      "screenResolution": "1080x2400",
      "installedApps": ["com.tencent.mm"]
    }
  }'
```

#### 获取脚本测试
```bash
# 使用上一步返回的sessionId
curl http://localhost:8080/api/v1/script/{sessionId}
```

### 2. 客户端测试

#### 基本功能测试
1. 启动小田螺助手应用
2. 在文本输入框中输入: "打开微信"
3. 点击"执行指令"按钮
4. 观察执行进度和日志

#### 预期行为
- 显示"处理中..."状态
- 显示执行进度条
- 显示执行日志
- 模拟执行完成

### 3. 端到端测试

#### 完整流程测试
1. 确保服务端运行在 localhost:8080
2. 启动客户端应用
3. 输入测试指令
4. 验证客户端与服务端通信
5. 检查执行结果

## 🔧 故障排除

### 常见问题

#### 1. 服务端启动失败
**问题**: `JAVA_HOME is not set`
**解决**: 
```bash
# Windows
set JAVA_HOME=C:\Program Files\Java\jdk-17

# Linux/Mac
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
```

#### 2. 客户端网络连接失败
**问题**: 无法连接到服务端
**解决**:
- 检查服务端是否启动 (访问健康检查接口)
- 确认网络地址配置 (模拟器使用 ********:8080)
- 检查防火墙设置

#### 3. 构建失败
**问题**: 依赖下载失败
**解决**:
- 检查网络连接
- 使用 `--refresh-dependencies` 强制刷新
- 清理构建缓存: `./gradlew clean`

#### 4. APK安装失败
**问题**: 安装被阻止
**解决**:
- 启用"未知来源"安装
- 检查设备存储空间
- 卸载旧版本后重新安装

### 调试技巧

#### 服务端调试
```bash
# 启用详细日志
./gradlew :conch-server:bootRun --debug

# 查看应用日志
tail -f logs/spring.log
```

#### 客户端调试
```bash
# 查看设备日志
adb logcat | grep ConchClient

# 查看网络请求
adb logcat | grep OkHttp
```

## 📊 性能监控

### 服务端监控
- 访问: http://localhost:8080/actuator/health
- 监控内存使用、响应时间等指标

### 客户端监控
- 使用Android Studio的Profiler
- 监控内存、CPU、网络使用情况

## 🔄 开发工作流

### 日常开发
1. 修改代码
2. 重新构建: `./gradlew :conch-android:assembleDebug`
3. 安装测试: `adb install -r app.apk`
4. 测试功能
5. 查看日志调试

### 服务端开发
1. 修改代码
2. 重启服务: `Ctrl+C` 然后 `./gradlew :conch-server:bootRun`
3. 测试API接口
4. 验证客户端集成

## 🎯 下一步开发

### 优先级1: 核心功能
- [ ] 集成真实的AI服务 (OpenAI/Claude API)
- [ ] 完善Assists框架集成
- [ ] 实现真实的自动化操作

### 优先级2: 用户体验
- [ ] 添加语音识别功能
- [ ] 完善UI界面设计
- [ ] 添加历史记录功能

### 优先级3: 系统完善
- [ ] 添加用户认证
- [ ] 实现数据持久化
- [ ] 添加错误报告机制

项目已经具备了完整的基础架构，可以开始进行功能开发和测试！
