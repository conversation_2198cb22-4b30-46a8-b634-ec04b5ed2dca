<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff">

    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:flexWrap="wrap">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="点击" />
        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="手势点击" />
        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="手势点击" />
        <Button
            android:id="@+id/btn_screen_capture"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="请求屏幕录制" />
        <Button
            android:id="@+id/btn_take_screenshot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="截取屏幕" />
        <Button
            android:id="@+id/btn_disable_pull_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="禁止下拉通知栏" />

        <Button
            android:id="@+id/btn_listener_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="通知监听（包括Toast）"
            android:textAllCaps="false" />
        <Button
            android:id="@+id/btn_collect_energy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="蚂蚁森林能量自动收集"
            android:textAllCaps="false" />
    </com.google.android.flexbox.FlexboxLayout>

</FrameLayout>