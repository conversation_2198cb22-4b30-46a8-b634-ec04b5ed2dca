# 小田螺助手 - 原型设计文档

## 📋 项目概述

小田螺助手是一个基于语音交互的智能手机自动化助手，通过自然语言指令控制手机应用，实现复杂的自动化操作。

### 核心特性
- 🎤 **语音交互**: 支持自然语言语音指令
- 🤖 **智能理解**: AI驱动的意图识别和任务分解
- 📱 **自动操作**: 基于Assists框架的无障碍服务操作
- 🔄 **反馈学习**: 实时反馈和策略优化
- 🌐 **云端智能**: 服务端AI决策，客户端执行

---

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    %% 用户交互层
    A[用户语音输入] --> B[小田螺Android客户端]

    %% 客户端模块
    subgraph "📱 客户端职责"
        B --> C[语音识别模块STT]
        C --> D[指令预处理]
        D --> E[网络通信模块]

        %% 执行引擎
        E --> F[脚本执行引擎]
        F --> G[Assists框架调用]
        G --> H[无障碍服务操作]
        H --> I[目标应用操作]

        %% 反馈收集
        I --> J[执行结果收集]
        J --> K[屏幕截图]
        K --> L[状态信息收集]
        L --> M[反馈数据打包]

        %% 结果展示
        E --> N[最终结果接收]
        N --> O[进度展示界面]
    end

    %% 服务端模块
    subgraph "🖥️ 服务端职责"
        %% AI决策层
        Q[意图识别引擎] --> R[任务分解器]
        R --> S[执行路径规划]

        %% 脚本生成层
        S --> T[动态脚本生成器]

        %% 反馈处理层
        W[执行结果分析器] --> X[图像识别理解]
        X --> Y[成功率评估]
        Y --> Z[决策引擎]

        %% 策略调整
        Z --> AA{继续执行?}
        AA -->|是| BB[脚本优化器]
        AA -->|否| CC[任务完成判断]
        BB --> S

        %% 知识库
        DD[应用操作知识库]
        EE[脚本模板库]
        FF[异常处理经验库]

        DD --> T
        EE --> T
        FF --> BB
    end

    %% 数据流向
    E --> Q
    T --> E

    M --> W
    CC --> E
```

### 技术栈选择

#### 客户端 (Android)
- **开发语言**: Kotlin
- **UI框架**: Android Jetpack Compose
- **网络通信**: Retrofit + OkHttp + WebSocket
- **语音识别**: 暂时空在那里，暂且先用一个输入框替代，等有方案再替换成语音识别
- **自动化框架**: Assists框架 (现有)
- **数据存储**: Room Database
- **依赖注入**: Hilt

#### 服务端 (Backend)
- **开发语言**: Kotlin
- **框架**: Spring Boot 3.x
- **数据库**: PostgreSQL
- **AI集成**: OpenAI API / Claude API / deepseek / 硅基流动 / 谷歌gemmini
- **图像处理**: OpenCV
- **消息队列**: RabbitMQ
- **缓存**: Redis
- **监控**: Micrometer + Prometheus

---

## 📱 客户端模块设计

### 1. 核心模块架构

```
conch-android/
├── src/main/java/com/conch/client/
│   ├── models/                 # 数据模型
│   │   ├── VoiceCommand.kt    # 语音指令模型
│   │   ├── ExecutionScript.kt # 执行脚本模型
│   │   ├── FeedbackData.kt    # 反馈数据模型
│   │   └── TaskSession.kt     # 任务会话模型
│   │
│   ├── network/               # 网络通信层
│   │   ├── ApiService.kt      # REST API接口
│   │   ├── WebSocketClient.kt # WebSocket客户端
│   │   ├── NetworkManager.kt  # 网络管理器
│   │   └── RetryPolicy.kt     # 重试策略
│   │
│   ├── input/                 # 输入处理模块
│   │   ├── TextInputProcessor.kt  # 文本输入处理
│   │   ├── CommandValidator.kt    # 指令验证
│   │   ├── InputFormatter.kt      # 输入格式化
│   │   └── VoiceInputPlaceholder.kt  # 语音输入占位(未来扩展)
│   │
│   ├── executor/              # 脚本执行引擎
│   │   ├── ScriptExecutor.kt      # 脚本执行器
│   │   ├── AssistsIntegration.kt  # Assists框架集成
│   │   ├── ActionPerformer.kt     # 动作执行器
│   │   └── ExecutionMonitor.kt    # 执行监控
│   │
│   ├── feedback/              # 反馈收集模块
│   │   ├── ScreenCapture.kt       # 屏幕截图
│   │   ├── StateCollector.kt      # 状态收集器
│   │   ├── ResultAnalyzer.kt      # 结果分析
│   │   └── FeedbackManager.kt     # 反馈管理器
│   │
│   ├── ui/                    # 用户界面
│   │   ├── MainActivity.kt        # 主界面
│   │   ├── VoiceInputScreen.kt    # 语音输入界面
│   │   ├── ProgressScreen.kt      # 进度展示界面
│   │   ├── HistoryScreen.kt       # 历史记录界面
│   │   └── SettingsScreen.kt      # 设置界面
│   │
│   └── utils/                 # 工具类
│       ├── PermissionManager.kt   # 权限管理
│       ├── DeviceInfoCollector.kt # 设备信息收集
│       ├── CacheManager.kt        # 缓存管理
│       └── LogManager.kt          # 日志管理
```

### 2. 关键组件设计

#### 文本输入处理模块 (TextInputProcessor)
```kotlin
class TextInputProcessor {
    // 处理文本输入
    suspend fun processTextInput(text: String): TextCommand

    // 指令预处理和验证
    fun preprocessCommand(rawText: String): TextCommand

    // 格式化和标准化
    fun formatCommand(command: TextCommand): FormattedCommand

    // 为未来语音识别预留接口
    suspend fun processVoiceInput(audioData: ByteArray): TextCommand // 占位方法
}
```

#### 脚本执行引擎 (ScriptExecutor)
```kotlin
class ScriptExecutor {
    // 执行服务端脚本
    suspend fun executeScript(script: ExecutionScript): ExecutionResult

    // 监控执行状态
    fun monitorExecution(): Flow<ExecutionStatus>

    // 异常处理和恢复
    suspend fun handleExecutionError(error: ExecutionError): RecoveryAction
}
```

#### 反馈收集器 (FeedbackManager)
```kotlin
class FeedbackManager {
    // 收集执行反馈
    suspend fun collectFeedback(): FeedbackData

    // 屏幕截图
    suspend fun captureScreen(): Bitmap

    // 状态信息收集
    fun collectDeviceState(): DeviceState
}
```

### 3. 数据模型设计

#### 文本指令模型
```kotlin
data class TextCommand(
    val id: String,
    val rawText: String,
    val processedText: String,
    val inputType: InputType, // TEXT, VOICE(未来)
    val timestamp: Long,
    val deviceInfo: DeviceInfo
)

enum class InputType {
    TEXT,           // 当前使用的文本输入
    VOICE_FUTURE    // 未来的语音输入占位
}
```

#### 执行脚本模型
```kotlin
data class ExecutionScript(
    val id: String,
    val sessionId: String,
    val actions: List<Action>,
    val metadata: ScriptMetadata,
    val retryPolicy: RetryPolicy
)

data class Action(
    val type: ActionType,
    val target: ActionTarget,
    val parameters: Map<String, Any>,
    val timeout: Long
)
```

#### 反馈数据模型
```kotlin
data class FeedbackData(
    val sessionId: String,
    val executionId: String,
    val screenshot: String?, // Base64编码
    val deviceState: DeviceState,
    val executionResult: ExecutionResult,
    val timestamp: Long
)
```

---

## 🖥️ 服务端模块设计

### 1. 核心模块架构

```
conch-server/
├── src/main/kotlin/com/conch/server/
│   ├── models/                    # 数据模型
│   │   ├── dto/                   # 数据传输对象
│   │   │   ├── VoiceCommandDto.kt
│   │   │   ├── ExecutionScriptDto.kt
│   │   │   └── FeedbackDto.kt
│   │   ├── entity/                # 数据库实体
│   │   │   ├── TaskSession.kt
│   │   │   ├── ExecutionHistory.kt
│   │   │   └── UserProfile.kt
│   │   └── domain/                # 领域模型
│   │       ├── Intent.kt
│   │       ├── Task.kt
│   │       └── ExecutionPlan.kt
│   │
│   ├── controller/                # REST API控制器
│   │   ├── VoiceController.kt     # 语音指令接口
│   │   ├── ExecutionController.kt # 执行控制接口
│   │   ├── FeedbackController.kt  # 反馈处理接口
│   │   └── WebSocketController.kt # WebSocket接口
│   │
│   ├── service/                   # 业务逻辑层
│   │   ├── ai/                    # AI决策服务
│   │   │   ├── IntentRecognitionService.kt
│   │   │   ├── TaskDecompositionService.kt
│   │   │   ├── PathPlanningService.kt
│   │   │   └── DecisionEngineService.kt
│   │   │
│   │   ├── script/                # 脚本生成服务
│   │   │   ├── ScriptGeneratorService.kt
│   │   │   ├── TemplateManagerService.kt
│   │   │   ├── ScriptOptimizerService.kt
│   │   │   └── ActionLibraryService.kt
│   │   │
│   │   ├── feedback/              # 反馈处理服务
│   │   │   ├── FeedbackAnalysisService.kt
│   │   │   ├── ImageRecognitionService.kt
│   │   │   ├── SuccessRateService.kt
│   │   │   └── ErrorRecoveryService.kt
│   │   │
│   │   └── knowledge/             # 知识库服务
│   │       ├── AppKnowledgeService.kt
│   │       ├── ScriptTemplateService.kt
│   │       ├── ExceptionHandlingService.kt
│   │       └── LearningService.kt
│   │
│   ├── repository/                # 数据访问层
│   │   ├── TaskSessionRepository.kt
│   │   ├── ExecutionHistoryRepository.kt
│   │   ├── UserProfileRepository.kt
│   │   └── KnowledgeBaseRepository.kt
│   │
│   ├── websocket/                 # WebSocket处理
│   │   ├── WebSocketHandler.kt
│   │   ├── SessionManager.kt
│   │   └── MessageRouter.kt
│   │
│   └── config/                    # 配置类
│       ├── DatabaseConfig.kt
│       ├── RedisConfig.kt
│       ├── WebSocketConfig.kt
│       └── AIServiceConfig.kt
```

### 2. 关键服务设计

#### 意图识别服务 (IntentRecognitionService)
```kotlin
@Service
class IntentRecognitionService {
    // 识别用户意图
    suspend fun recognizeIntent(voiceCommand: VoiceCommand): Intent

    // 多轮对话管理
    suspend fun processConversation(sessionId: String, message: String): ConversationResult

    // 意图确认和澄清
    suspend fun clarifyIntent(intent: Intent): ClarificationRequest?
}
```

#### 脚本生成服务 (ScriptGeneratorService)
```kotlin
@Service
class ScriptGeneratorService {
    // 生成执行脚本
    suspend fun generateScript(intent: Intent, deviceInfo: DeviceInfo): ExecutionScript

    // 脚本优化
    suspend fun optimizeScript(script: ExecutionScript, feedback: FeedbackData): ExecutionScript

    // 模板匹配
    fun matchTemplate(intent: Intent): ScriptTemplate?
}
```

#### 反馈分析服务 (FeedbackAnalysisService)
```kotlin
@Service
class FeedbackAnalysisService {
    // 分析执行反馈
    suspend fun analyzeFeedback(feedback: FeedbackData): AnalysisResult

    // 图像识别和理解
    suspend fun analyzeScreenshot(screenshot: String): ScreenAnalysis

    // 成功率评估
    suspend fun evaluateSuccess(feedback: FeedbackData): SuccessEvaluation
}
```

---

## 🔄 通信协议设计

### 1. API接口规范

#### REST API端点
```
POST /api/v1/voice/command          # 提交语音指令
GET  /api/v1/session/{id}           # 获取会话状态
POST /api/v1/feedback               # 提交执行反馈
GET  /api/v1/script/{sessionId}     # 获取执行脚本
POST /api/v1/session/complete       # 完成任务会话
```

#### WebSocket消息类型
```kotlin
sealed class WebSocketMessage {
    data class VoiceCommand(val command: VoiceCommandDto) : WebSocketMessage()
    data class ExecutionScript(val script: ExecutionScriptDto) : WebSocketMessage()
    data class ExecutionFeedback(val feedback: FeedbackDto) : WebSocketMessage()
    data class TaskProgress(val progress: ProgressDto) : WebSocketMessage()
    data class TaskComplete(val result: TaskResultDto) : WebSocketMessage()
    data class Error(val error: ErrorDto) : WebSocketMessage()
}
```

### 2. 数据传输格式

#### 语音指令请求
```json
{
  "sessionId": "uuid",
  "voiceCommand": {
    "text": "打开微信发消息给张三",
    "confidence": 0.95,
    "timestamp": 1640995200000
  },
  "deviceInfo": {
    "model": "Xiaomi 12",
    "androidVersion": "12",
    "screenResolution": "1080x2400",
    "installedApps": ["com.tencent.mm", "com.taobao.taobao"]
  }
}
```

#### 执行脚本响应
```json
{
  "sessionId": "uuid",
  "scriptId": "script-uuid",
  "actions": [
    {
      "type": "CLICK",
      "target": {
        "type": "TEXT",
        "value": "微信",
        "fallback": {
          "type": "COORDINATE",
          "x": 540,
          "y": 1200
        }
      },
      "timeout": 5000
    },
    {
      "type": "INPUT_TEXT",
      "target": {
        "type": "ID",
        "value": "search_input"
      },
      "parameters": {
        "text": "张三"
      },
      "timeout": 3000
    }
  ],
  "metadata": {
    "estimatedDuration": 15000,
    "retryCount": 3,
    "priority": "HIGH"
  }
}
```

#### 反馈数据格式
```json
{
  "sessionId": "uuid",
  "scriptId": "script-uuid",
  "executionResult": {
    "status": "SUCCESS",
    "completedActions": 2,
    "totalActions": 3,
    "executionTime": 8500,
    "errors": []
  },
  "screenshot": "base64-encoded-image",
  "deviceState": {
    "currentApp": "com.tencent.mm",
    "screenOn": true,
    "networkConnected": true,
    "batteryLevel": 85
  },
  "timestamp": 1640995215000
}
```

---

## 🔄 核心工作流程

### 1. 完整交互流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 客户端
    participant S as 服务端
    participant AI as AI引擎

    U->>C: 语音指令
    C->>C: 语音识别(STT)
    C->>S: 发送语音指令
    S->>AI: 意图识别
    AI->>S: 返回意图分析
    S->>S: 任务分解
    S->>S: 脚本生成
    S->>C: 发送执行脚本
    C->>C: 执行自动化操作
    C->>S: 发送执行反馈
    S->>AI: 分析反馈结果
    AI->>S: 返回分析结果

    alt 需要继续执行
        S->>S: 优化脚本
        S->>C: 发送新脚本
        C->>C: 继续执行
    else 任务完成
        S->>C: 发送完成通知
        C->>U: 显示执行结果
    end
```

### 2. 错误处理流程

```mermaid
flowchart TD
    A[执行动作] --> B{执行成功?}
    B -->|是| C[继续下一动作]
    B -->|否| D[收集错误信息]
    D --> E[发送错误反馈]
    E --> F[服务端分析错误]
    F --> G{可恢复?}
    G -->|是| H[生成恢复脚本]
    G -->|否| I[任务失败]
    H --> J[执行恢复动作]
    J --> K{恢复成功?}
    K -->|是| C
    K -->|否| L{重试次数<限制?}
    L -->|是| M[等待后重试]
    L -->|否| I
    M --> A
```

### 3. 会话管理流程

#### 会话生命周期
1. **会话创建**: 用户发起语音指令时创建新会话
2. **会话活跃**: 执行过程中保持会话状态
3. **会话暂停**: 等待用户确认或处理异常时暂停
4. **会话恢复**: 用户确认后恢复执行
5. **会话完成**: 任务成功完成或用户主动终止
6. **会话清理**: 清理临时数据，保存历史记录

#### 状态管理
```kotlin
enum class SessionState {
    CREATED,        // 已创建
    PROCESSING,     // 处理中
    EXECUTING,      // 执行中
    WAITING,        // 等待反馈
    PAUSED,         // 暂停
    COMPLETED,      // 已完成
    FAILED,         // 失败
    CANCELLED       // 已取消
}
```

---

## 🎨 用户界面设计

### 1. 主要界面结构

#### 主界面 (MainActivity)
```kotlin
@Composable
fun MainScreen() {
    Column {
        // 顶部状态栏
        TopAppBar(
            title = { Text("小田螺助手") },
            actions = {
                IconButton(onClick = { /* 设置 */ }) {
                    Icon(Icons.Default.Settings, contentDescription = "设置")
                }
            }
        )

        // 中央文本输入区域
        TextInputSection(
            inputText = viewModel.inputText,
            onTextChange = { viewModel.updateInputText(it) },
            onSubmit = { viewModel.submitCommand() },
            isProcessing = viewModel.isProcessing
        )

        // 执行进度显示
        if (viewModel.isExecuting) {
            ExecutionProgressSection(
                progress = viewModel.executionProgress,
                currentAction = viewModel.currentAction
            )
        }

        // 底部导航
        BottomNavigation {
            BottomNavigationItem(
                icon = { Icon(Icons.Default.History, contentDescription = "历史") },
                label = { Text("历史") },
                selected = false,
                onClick = { /* 导航到历史页面 */ }
            )
        }
    }
}
```

#### 文本输入界面
```kotlin
@Composable
fun TextInputSection(
    inputText: String,
    onTextChange: (String) -> Unit,
    onSubmit: () -> Unit,
    isProcessing: Boolean
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(32.dp)
    ) {
        // 输入框
        OutlinedTextField(
            value = inputText,
            onValueChange = onTextChange,
            label = { Text("请输入指令") },
            placeholder = { Text("例如：打开微信发消息给张三") },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp),
            maxLines = 3,
            enabled = !isProcessing
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 提交按钮
        Button(
            onClick = onSubmit,
            enabled = inputText.isNotBlank() && !isProcessing,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (isProcessing) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("处理中...")
            } else {
                Text("执行指令")
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 提示文字
        Text(
            text = "暂时使用文本输入，未来将支持语音指令",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
```

### 2. 执行进度界面

#### 进度展示组件
```kotlin
@Composable
fun ExecutionProgressSection(
    progress: ExecutionProgress,
    currentAction: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            // 进度条
            LinearProgressIndicator(
                progress = progress.percentage,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 当前动作
            Text(
                text = "正在执行: $currentAction",
                style = MaterialTheme.typography.bodyMedium
            )

            // 步骤列表
            LazyColumn {
                items(progress.steps) { step ->
                    StepItem(step = step)
                }
            }
        }
    }
}

@Composable
fun StepItem(step: ExecutionStep) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(vertical = 4.dp)
    ) {
        // 状态图标
        Icon(
            imageVector = when (step.status) {
                StepStatus.COMPLETED -> Icons.Default.CheckCircle
                StepStatus.EXECUTING -> Icons.Default.PlayArrow
                StepStatus.PENDING -> Icons.Default.Circle
                StepStatus.FAILED -> Icons.Default.Error
            },
            contentDescription = null,
            tint = when (step.status) {
                StepStatus.COMPLETED -> Color.Green
                StepStatus.EXECUTING -> Color.Blue
                StepStatus.PENDING -> Color.Gray
                StepStatus.FAILED -> Color.Red
            }
        )

        Spacer(modifier = Modifier.width(8.dp))

        // 步骤描述
        Text(
            text = step.description,
            style = MaterialTheme.typography.bodySmall
        )
    }
}
```

---

## 🔧 技术实现细节

### 1. 文本输入处理实现

#### 文本输入处理器
```kotlin
class TextInputProcessor {

    // 处理用户文本输入
    suspend fun processTextInput(rawText: String): TextCommand {
        val processedText = preprocessText(rawText)
        return TextCommand(
            id = UUID.randomUUID().toString(),
            rawText = rawText,
            processedText = processedText,
            inputType = InputType.TEXT,
            timestamp = System.currentTimeMillis(),
            deviceInfo = DeviceInfoCollector.collect()
        )
    }

    // 文本预处理
    private fun preprocessText(text: String): String {
        return text.trim()
            .replace(Regex("\\s+"), " ") // 标准化空格
            .lowercase() // 转小写便于处理
    }

    // 指令验证
    fun validateCommand(command: TextCommand): ValidationResult {
        return when {
            command.processedText.isBlank() -> ValidationResult.Error("指令不能为空")
            command.processedText.length > 200 -> ValidationResult.Error("指令过长")
            else -> ValidationResult.Success
        }
    }
}

// 为未来语音识别预留的接口
interface VoiceInputProcessor {
    suspend fun processVoiceInput(audioData: ByteArray): TextCommand
    suspend fun startVoiceRecognition(): Flow<String>
    fun switchRecognitionMode(mode: RecognitionMode)
}

// 占位实现，未来替换为真实的语音识别
class VoiceInputPlaceholder : VoiceInputProcessor {
    override suspend fun processVoiceInput(audioData: ByteArray): TextCommand {
        TODO("语音识别功能待实现")
    }

    override suspend fun startVoiceRecognition(): Flow<String> {
        TODO("语音识别功能待实现")
    }

    override fun switchRecognitionMode(mode: RecognitionMode) {
        TODO("语音识别功能待实现")
    }
}
```

### 2. Assists框架集成

#### 脚本执行器实现
```kotlin
class AssistsScriptExecutor : ScriptExecutor {
    private val assistsService = AssistsService.getInstance()

    override suspend fun executeScript(script: ExecutionScript): ExecutionResult {
        val results = mutableListOf<ActionResult>()

        for (action in script.actions) {
            try {
                val result = executeAction(action)
                results.add(result)

                if (!result.success) {
                    return ExecutionResult.failure(results, result.error)
                }

                // 等待动作完成
                delay(action.timeout)

            } catch (e: Exception) {
                return ExecutionResult.failure(results, e.message)
            }
        }

        return ExecutionResult.success(results)
    }

    private suspend fun executeAction(action: Action): ActionResult {
        return when (action.type) {
            ActionType.CLICK -> performClick(action.target)
            ActionType.INPUT_TEXT -> performInput(action.target, action.parameters["text"] as String)
            ActionType.SWIPE -> performSwipe(action.target, action.parameters)
            ActionType.WAIT -> performWait(action.parameters["duration"] as Long)
            else -> ActionResult.failure("不支持的动作类型: ${action.type}")
        }
    }

    private suspend fun performClick(target: ActionTarget): ActionResult {
        return when (target.type) {
            TargetType.TEXT -> assistsService.clickByText(target.value)
            TargetType.ID -> assistsService.clickById(target.value)
            TargetType.COORDINATE -> assistsService.clickByCoordinate(target.x, target.y)
            else -> ActionResult.failure("不支持的目标类型: ${target.type}")
        }
    }
}
```

### 3. 网络通信实现

#### WebSocket客户端
```kotlin
class ConchWebSocketClient {
    private var webSocket: WebSocket? = null
    private val messageFlow = MutableSharedFlow<WebSocketMessage>()

    fun connect(url: String) {
        val request = Request.Builder()
            .url(url)
            .build()

        webSocket = OkHttpClient().newWebSocket(request, object : WebSocketListener() {
            override fun onMessage(webSocket: WebSocket, text: String) {
                val message = Json.decodeFromString<WebSocketMessage>(text)
                messageFlow.tryEmit(message)
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                // 处理连接失败，实现重连机制
                scheduleReconnect()
            }
        })
    }

    fun sendMessage(message: WebSocketMessage) {
        val json = Json.encodeToString(message)
        webSocket?.send(json)
    }

    fun observeMessages(): Flow<WebSocketMessage> = messageFlow.asSharedFlow()
}
```

#### REST API客户端
```kotlin
interface ConchApiService {
    @POST("api/v1/voice/command")
    suspend fun submitVoiceCommand(@Body command: VoiceCommandDto): Response<TaskSessionDto>

    @POST("api/v1/feedback")
    suspend fun submitFeedback(@Body feedback: FeedbackDto): Response<Unit>

    @GET("api/v1/session/{id}")
    suspend fun getSession(@Path("id") sessionId: String): Response<TaskSessionDto>

    @POST("api/v1/session/complete")
    suspend fun completeSession(@Body completion: SessionCompletionDto): Response<Unit>
}
```

---

## 🚀 部署和配置

### 1. 客户端构建配置

#### build.gradle (客户端)
```kotlin
android {
    compileSdk 34

    defaultConfig {
        applicationId "com.conch.client"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"
    }

    buildFeatures {
        compose = true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.8"
    }
}

dependencies {
    // Android核心
    implementation "androidx.core:core-ktx:1.12.0"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
    implementation "androidx.activity:activity-compose:1.8.2"

    // Compose UI
    implementation platform("androidx.compose:compose-bom:2024.02.00")
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.material3:material3"

    // 网络通信
    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:okhttp:4.12.0"
    implementation "com.squareup.okhttp3:logging-interceptor:4.12.0"

    // 语音识别 (暂时注释，未来启用)
    // implementation "com.baidu.aip:java-sdk:4.16.8"

    // 依赖注入
    implementation "com.google.dagger:hilt-android:2.48"
    kapt "com.google.dagger:hilt-compiler:2.48"

    // 数据库
    implementation "androidx.room:room-runtime:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"

    // Assists框架
    implementation project(":assists")
}
```

### 2. 服务端配置

#### build.gradle.kts (服务端)
```kotlin
plugins {
    kotlin("jvm") version "1.9.22"
    kotlin("plugin.spring") version "1.9.22"
    id("org.springframework.boot") version "3.2.2"
    id("io.spring.dependency-management") version "1.1.4"
}

dependencies {
    // Spring Boot
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-websocket")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")

    // 数据库
    implementation("org.postgresql:postgresql")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")

    // AI集成
    implementation("com.theokanning.openai-gpt3-java:service:0.18.2")

    // 图像处理
    implementation("org.opencv:opencv-java:4.8.0")

    // JSON处理
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")

    // 协程支持
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
}
```

#### application.yml (服务端配置)
```yaml
server:
  port: 8080

spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:conch_user}
    password: ${DB_PASSWORD:conch_pass}
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  redis:
    host: localhost
    port: 6379
    password: ${REDIS_PASSWORD:}

  websocket:
    allowed-origins: "*"

# AI服务配置
ai:
  openai:
    api-key: ${OPENAI_API_KEY}
    model: gpt-4
    max-tokens: 2048

  baidu:
    app-id: ${BAIDU_APP_ID}
    api-key: ${BAIDU_API_KEY}
    secret-key: ${BAIDU_SECRET_KEY}

# 应用配置
conch:
  session:
    timeout: 300000  # 5分钟
    max-concurrent: 100

  script:
    max-actions: 50
    default-timeout: 5000

  feedback:
    image-max-size: 2097152  # 2MB
    retention-days: 30

logging:
  level:
    com.conch: DEBUG
    org.springframework.web: INFO
```

---

## 📋 开发计划和里程碑

### 第一阶段：基础架构 (2-3周)

#### 客户端基础模块
- [ ] 项目结构搭建和依赖配置
- [ ] 基础UI界面 (主界面、文本输入界面)
- [ ] 文本输入处理模块 (为未来语音识别预留接口)
- [ ] 网络通信模块 (Retrofit + WebSocket)
- [ ] Assists框架集成和基础脚本执行

#### 服务端基础模块
- [ ] Spring Boot项目搭建
- [ ] 数据库设计和实体定义
- [ ] REST API基础接口
- [ ] WebSocket通信实现
- [ ] AI服务集成 (OpenAI API)

#### 通信协议
- [ ] API接口规范定义 (OpenAPI)
- [ ] 数据传输格式设计
- [ ] 错误处理机制
- [ ] 基础的端到端通信测试

### 第二阶段：核心功能 (3-4周)

#### 智能决策功能
- [ ] 意图识别和自然语言理解
- [ ] 任务分解和执行路径规划
- [ ] 动态脚本生成
- [ ] 应用操作知识库建设

#### 执行和反馈
- [ ] 复杂自动化操作实现
- [ ] 屏幕截图和状态收集
- [ ] 执行结果分析和成功率评估
- [ ] 错误恢复和重试机制

#### 用户体验
- [ ] 进度展示和实时反馈
- [ ] 历史记录和会话管理
- [ ] 用户设置和个性化配置
- [ ] 离线模式和缓存机制

### 第三阶段：优化和测试 (2-3周)

#### 性能优化
- [ ] 网络通信优化和压缩
- [ ] 脚本执行效率提升
- [ ] 内存和电量优化
- [ ] 并发处理和资源管理

#### 稳定性和测试
- [ ] 单元测试和集成测试
- [ ] 异常场景测试
- [ ] 多设备兼容性测试
- [ ] 压力测试和性能基准

#### 部署和发布
- [ ] CI/CD流水线搭建
- [ ] 生产环境部署配置
- [ ] 监控和日志系统
- [ ] 用户文档和使用指南

---

## 🎯 关键技术挑战和解决方案

### 1. 文本输入处理和未来语音扩展
**当前方案**: 使用文本输入框替代语音识别
**未来挑战**: 复杂指令的准确识别，噪音环境适应
**解决方案**:
- 当前：完善的文本预处理和验证
- 未来：多引擎融合 (在线+离线)
- 上下文理解和纠错
- 用户确认机制

### 2. 意图理解复杂性
**挑战**: 自然语言的歧义性和复杂性
**解决方案**:
- 领域特定的prompt工程
- 多轮对话澄清
- 意图模板库建设

### 3. 设备兼容性
**挑战**: 不同手机、不同应用版本的适配
**解决方案**:
- 多策略元素定位
- 动态适配机制
- 降级和备用方案

### 4. 执行可靠性
**挑战**: 自动化操作的稳定性和成功率
**解决方案**:
- 智能重试机制
- 实时状态监控
- 错误恢复策略

这个原型文档为小田螺助手项目提供了完整的技术架构、实现细节和开发计划，可以作为后续开发的重要参考。
```