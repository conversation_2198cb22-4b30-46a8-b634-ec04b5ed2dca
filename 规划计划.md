
# 小田螺助手 - 职责划分方案

系统架构图：

```mermaid
graph TB
    %% 用户交互层
    A[用户语音输入] --> B[小田螺Android客户端]

    %% 客户端模块
    subgraph "📱 客户端职责"
        B --> C[语音识别模块STT]
        C --> D[指令预处理]
        D --> E[网络通信模块]

        %% 执行引擎
        E --> F[脚本执行引擎]
        F --> G[Assists框架调用]
        G --> H[无障碍服务操作]
        H --> I[目标应用操作]

        %% 反馈收集
        I --> J[执行结果收集]
        J --> K[屏幕截图]
        K --> L[状态信息收集]
        L --> M[反馈数据打包]

        %% 结果展示
        E --> N[最终结果接收]
        N --> O[进度展示界面]
    end

    %% 服务端模块
    subgraph "🖥️ 服务端职责"
        %% AI决策层
        Q[意图识别引擎] --> R[任务分解器]
        R --> S[执行路径规划]

        %% 脚本生成层
        S --> T[动态脚本生成器]

        %% 反馈处理层
        W[执行结果分析器] --> X[图像识别理解]
        X --> Y[成功率评估]
        Y --> Z[决策引擎]

        %% 策略调整
        Z --> AA{继续执行?}
        AA -->|是| BB[脚本优化器]
        AA -->|否| CC[任务完成判断]
        BB --> S

        %% 知识库
        DD[应用操作知识库]
        EE[脚本模板库]
        FF[异常处理经验库]

        DD --> T
        EE --> T
        FF --> BB
    end

    %% 数据流向
    E --> Q
    T --> E

    M --> W
    CC --> E
```


## 🎯 总体原则

**客户端职责**: 用户交互、设备操作、执行反馈
**服务端职责**: AI智能决策、脚本生成、任务编排

---

## 📱 客户端 (Android App) 职责

### 1. 用户交互层
- **语音输入处理**
  - 语音录制和实时识别
  - 语音转文字 (STT)
  - 语音指令预处理和格式化
  - 用户确认和交互反馈

- **结果展示**
  - 任务执行进度显示
  - 执行结果可视化
  - 错误信息提示
  - 用户操作历史记录

### 2. 设备操作层
- **无障碍服务管理**
  - Assists框架的初始化和配置
  - 无障碍权限申请和管理
  - 服务状态监控和恢复

- **脚本执行引擎**
  - 接收并解析服务端脚本
  - 调用Assists框架执行具体操作
  - 实时监控执行状态
  - 异常捕获和本地处理

- **设备信息收集**
  - 屏幕截图和元素信息获取
  - 当前应用状态检测
  - 设备性能和网络状态监控
  - 执行环境上下文收集

### 3. 通信协调层
- **网络通信管理**
  - 与服务端的HTTP/WebSocket连接 WebSocket + HTTP 混合模式
  - 请求重试和网络异常处理
  - 数据压缩和传输优化
  - 离线模式和缓存管理

- **会话状态管理**
  - 任务会话的生命周期管理
  - 执行上下文的本地缓存
  - 断线重连和状态恢复

### 4. 本地数据管理
- **配置和缓存**
  - 用户偏好设置存储
  - 常用脚本模板缓存
  - 应用适配信息缓存
  - 执行历史本地存储

---

## 🖥️ 服务端职责 Kotlin + Spring Boot

### 1. AI智能决策层
- **意图理解和分析**
  - 自然语言处理和意图识别
  - 用户需求的语义分析
  - 上下文理解和多轮对话管理
  - 模糊指令的澄清和确认

- **任务规划和分解**
  - 复杂任务的分步骤分解
  - 执行路径的智能规划
  - 依赖关系分析和优化
  - 异常场景的预案设计

### 2. 脚本生成和优化
- **动态脚本生成**
  - 基于意图生成具体执行脚本
  - 设备适配和兼容性处理
  - 脚本模板管理和复用
  - 个性化定制和优化

- **执行策略调整**
  - 根据反馈动态调整执行策略
  - 错误恢复和重试机制设计
  - 执行路径的实时优化
  - 性能和成功率的持续改进

### 3. 反馈处理和决策
- **执行结果分析**
  - 客户端反馈的智能分析
  - 执行成功率和异常模式识别
  - 屏幕截图的图像识别和理解
  - 执行效果的评估和验证

- **下一步决策**
  - 继续执行 vs 停止的智能判断
  - 脚本修正和策略调整
  - 异常处理和错误恢复
  - 任务完成度评估

### 4. 数据管理和学习
- **用户行为分析**
  - 用户使用模式分析
  - 个性化偏好学习
  - 执行效率统计和优化
  - 用户满意度跟踪

- **知识库管理**
  - 应用操作知识库维护
  - 脚本模板库管理
  - 设备适配规则库
  - 异常处理经验库

---

## 🔄 交互流程中的职责分工

### 流程步骤详细分工

#### 步骤1: 语音输入处理
- **客户端**: 录制语音 → 语音识别 → 文字预处理
- **服务端**: 接收文字 → 意图理解 → 需求确认

#### 步骤2: 任务分析和脚本生成
- **客户端**: 发送设备信息和当前状态
- **服务端**: 任务分解 → 脚本生成 → 执行计划制定

#### 步骤3: 脚本执行
- **客户端**: 接收脚本 → 调用Assists框架 → 执行操作 → 收集反馈
- **服务端**: 等待反馈 → 准备下一步决策

#### 步骤4: 反馈处理
- **客户端**: 截图 → 状态收集 → 结果上报
- **服务端**: 分析反馈 → 评估执行效果 → 决策下一步

#### 步骤5: 迭代执行
- **客户端**: 接收新指令 → 继续执行或停止
- **服务端**: 脚本优化 → 策略调整 → 发送新指令

#### 步骤6: 结果展示
- **客户端**: 接收最终结果 → 用户界面展示 → 历史记录保存
- **服务端**: 任务总结 → 经验学习 → 数据统计

---

## 🎯 关键设计原则

### 1. 智能在云端，执行在本地
- **服务端**: 负责所有AI决策和智能分析
- **客户端**: 专注于设备操作和用户交互

### 2. 最小化数据传输
- **客户端**: 只上传必要的执行反馈和状态信息
- **服务端**: 只下发精确的执行指令

### 3. 离线容错能力
- **客户端**: 具备基础的离线执行能力
- **服务端**: 支持断线重连和状态恢复

---

## 📊 数据流向图

```
用户语音 → [客户端STT] → 文字指令 → [服务端AI] → 执行脚本
                                                        ↓
执行结果 ← [客户端展示] ← 最终决策 ← [服务端分析] ← 执行反馈
```

这样的职责划分确保了：
- **客户端轻量化**: 专注于设备操作和用户体验
- **服务端智能化**: 集中AI能力和复杂决策逻辑
- **数据安全性**: 敏感操作在本地，智能分析在云端
- **可扩展性**: 服务端可以支持多种客户端和设备类型

---

## 🏗️ 项目架构和构建部署

### 项目目录结构

基于现有Assists框架，采用**完全解耦的单仓库多模块**架构：

```
Assists/ (当前仓库根目录)
├── assists/                    # 现有核心框架 (保持不变)
├── assists-opcv/              # 现有opencv模块 (保持不变)
├── assists-mp/                # 现有录屏模块 (保持不变)
├── assists-web/               # 现有web模块 (保持不变)
├── simple/                    # 现有示例应用 (保持不变)
│
├── conch-android/              # 🆕 小田螺客户端模块 (完全独立)
│   ├── src/main/java/com/conch/client/
│   │   ├── models/            # 客户端专用数据模型
│   │   ├── network/           # 客户端网络层 (Retrofit + OkHttp)
│   │   ├── protocol/          # 客户端协议实现
│   │   ├── voice/             # 语音处理
│   │   ├── executor/          # 脚本执行引擎
│   │   ├── feedback/          # 反馈收集
│   │   └── ui/                # UI界面
│   └── build.gradle
│
├── conch-server/              # 🆕 小田螺服务端模块 (完全独立)
│   ├── src/main/kotlin/com/conch/server/
│   │   ├── models/            # 服务端专用数据模型
│   │   ├── controller/        # REST API控制器
│   │   ├── service/           # 业务逻辑层
│   │   │   ├── ai/            # AI决策服务
│   │   │   ├── script/        # 脚本生成服务
│   │   │   ├── feedback/      # 反馈处理服务
│   │   │   └── knowledge/     # 知识库服务
│   │   ├── repository/        # 数据访问层
│   │   ├── protocol/          # 服务端协议实现
│   │   └── websocket/         # WebSocket处理
│   └── build.gradle
│
├── api-spec/                  # 🆕 API规范文档 (OpenAPI)
│   ├── openapi.yaml          # API规范定义
│   ├── examples/             # 请求响应示例
│   └── docs/                 # 协议文档
│
├── scripts/                   # 🔧 构建和部署脚本
│   ├── build-android.sh       # 客户端独立构建
│   ├── build-server.sh       # 服务端独立构建
│   ├── deploy-server.sh      # 服务端部署脚本
│   ├── deploy-android.sh      # 客户端部署脚本
│   └── validate-api.sh       # API一致性验证
│
└── .github/workflows/         # CI/CD配置
    └── build-and-deploy.yml
```

### 构建和部署流程

#### 🔄 完全并行构建流程
```mermaid
graph TB
    A[开始构建] --> B[客户端构建]
    A --> C[服务端构建]

    B --> D[Android依赖解析]
    B --> E[Kotlin编译]
    B --> F[APK打包]

    C --> G[Spring Boot依赖解析]
    C --> H[Kotlin编译]
    C --> I[JAR打包]

    F --> J[客户端完成]
    I --> K[服务端完成]
```

#### 📦 构建产物
- **服务端**: `conch-server-{version}.jar`
- **客户端**: `conch-android-{type}-{version}-{date}.apk`

#### 🚀 部署策略
- **客户端部署**: ADB安装 → 手动分发 → 应用商店发布
- **服务端部署**: 本地运行 → 测试环境 → 生产环境

#### ⚡ 开发工作流
```bash
# 完全并行构建 - 无依赖等待
./gradlew :conch-android:build &
./gradlew :conch-server:build &
wait  # 等待两个构建完成

# 或者分别构建
./scripts/build-android.sh    # 客户端独立构建
./scripts/build-server.sh    # 服务端独立构建

# 日常开发
./gradlew :conch-server:bootRun          # 启动服务端
./gradlew :conch-android:installDebug     # 安装客户端

# API一致性验证
./scripts/validate-api.sh                # 验证协议一致性
```

#### 🎯 解耦架构优势
- **真正的并行开发**: 客户端和服务端团队完全独立
- **技术栈自由**: 各端选择最适合的技术和库
- **独立发布**: 不同的发布节奏和版本管理
- **故障隔离**: 一端问题不影响另一端开发
- **构建速度**: 无依赖等待，充分并行

#### 📋 协议一致性保证
- **OpenAPI规范**: 统一的API文档和规范
- **契约测试**: 自动化验证客户端和服务端协议兼容性
- **各端独立实现**: 使用最适合的数据模型和序列化方式

---

## 🚀 开发阶段规划

### 第一阶段：基础架构搭建 
- [ ] 创建三个新模块的基础结构
- [ ] 建立客户端和服务端的通信协议
- [ ] 集成语音识别基础功能
- [ ] 实现简单的指令执行流程

### 第二阶段：核心功能开发 
- [ ] 完善AI意图识别和脚本生成
- [ ] 实现复杂的自动化操作
- [ ] 建立反馈机制和错误处理
- [ ] 开发用户界面

### 第三阶段：优化和测试 
- [ ] 性能优化和稳定性提升
- [ ] 全面测试和bug修复
- [ ] 用户体验优化

开发建议和讨论 
1. 技术栈选择的规划很合理： 
• 客户端：Android + Kotlin + Assists框架 
• 服务端：Kotlin + Spring Boot 
• AI能力：接入大模型API（如GPT、Claude、国产大模型等）  
2. 关键技术挑战和解决方案 🎤 语音识别 (STT) 挑战：实时性、准确性、离线能力
建议： • 在线：百度语音、讯飞语音、Google Speech API • 离线：集成轻量级离线STT模型 • 混合模式：在线优先，离线备用  
🧠 意图理解 挑战：复杂指令的准确理解
建议： • 构建领域特定的prompt工程 • 建立指令模板库和意图分类体系 • 支持多轮对话澄清模糊指令  
🤖 脚本生成 挑战：将自然语言转换为可执行的Assists脚本
建议： • 建立操作原语库（点击、滑动、输入等） • 应用适配知识库（不同APP的元素定位规则） • 模板化脚本生成（常见操作的标准化）  
📱 设备适配 挑战：不同手机、不同APP版本的兼容性
建议： • 多策略元素定位（ID、文本、位置等） • 动态适配机制 • 失败重试和降级策略

