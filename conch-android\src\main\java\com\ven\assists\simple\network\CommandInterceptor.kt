package com.ven.assists.simple.network

import android.util.Log
import com.ven.assists.simple.App
import com.ven.assists.stepper.Step
import com.ven.assists.stepper.StepListener
import com.ven.assists.stepper.StepOperator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 指令拦截器
 * 在每次执行步骤之前将指令信息发送给服务端
 */
class CommandInterceptor : StepListener {
    
    companion object {
        private const val TAG = "CommandInterceptor"
    }
    
    private val interceptorScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 步骤开始时的拦截
     * 在这里发送指令信息到服务端
     */
    override fun onIntercept(step: StepOperator): Step? {
        // 异步发送指令信息到服务端
        interceptorScope.launch {
            sendCommandToServer(step)
        }
        
        // 返回null表示不拦截，继续执行原步骤
        return null
    }
    
    /**
     * 步骤开始时的回调
     */
    override fun onStepStart(step: StepOperator) {
        Log.d(TAG, "步骤开始执行: ${step.implClassName}:${step.step}")
    }
    
    /**
     * 步骤停止时的回调
     */
    override fun onStepStop() {
        Log.d(TAG, "步骤执行停止")
        // 发送停止指令到服务端
        interceptorScope.launch {
            sendStopCommandToServer()
        }
    }
    
    /**
     * 步骤异常时的回调
     */
    override fun onStepCatch(e: Throwable) {
        Log.e(TAG, "步骤执行异常", e)
        // 发送异常信息到服务端
        interceptorScope.launch {
            sendErrorCommandToServer(e)
        }
    }
    
    /**
     * 发送指令信息到服务端
     */
    private suspend fun sendCommandToServer(step: StepOperator) {
        try {
            val commandType = "STEP_EXECUTE"
            val commandData = buildCommandData(step)
            
            val result = App.networkManager.sendCommand(commandType, commandData)
            
            result.onSuccess { response: CommandResponseDto ->
                Log.d(TAG, "指令发送成功: ${response.message}")
            }.onFailure { error: Throwable ->
                Log.e(TAG, "指令发送失败", error)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "发送指令到服务端时发生异常", e)
        }
    }
    
    /**
     * 发送停止指令到服务端
     */
    private suspend fun sendStopCommandToServer() {
        try {
            val commandType = "STEP_STOP"
            val commandData = """
                {
                    "action": "stop",
                    "timestamp": ${System.currentTimeMillis()}
                }
            """.trimIndent()
            
            val result = App.networkManager.sendCommand(commandType, commandData)
            
            result.onSuccess { response: CommandResponseDto ->
                Log.d(TAG, "停止指令发送成功: ${response.message}")
            }.onFailure { error: Throwable ->
                Log.e(TAG, "停止指令发送失败", error)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "发送停止指令时发生异常", e)
        }
    }
    
    /**
     * 发送异常信息到服务端
     */
    private suspend fun sendErrorCommandToServer(error: Throwable) {
        try {
            val commandType = "STEP_ERROR"
            val commandData = """
                {
                    "action": "error",
                    "errorType": "${error.javaClass.simpleName}",
                    "errorMessage": "${error.message ?: "未知错误"}",
                    "stackTrace": "${error.stackTraceToString()}",
                    "timestamp": ${System.currentTimeMillis()}
                }
            """.trimIndent()
            
            val result = App.networkManager.sendCommand(commandType, commandData)
            
            result.onSuccess { response: CommandResponseDto ->
                Log.d(TAG, "异常信息发送成功: ${response.message}")
            }.onFailure { sendError: Throwable ->
                Log.e(TAG, "异常信息发送失败", sendError)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "发送异常信息时发生异常", e)
        }
    }
    
    /**
     * 构建指令数据
     */
    private fun buildCommandData(step: StepOperator): String {
        return """
            {
                "action": "execute",
                "stepClass": "${step.implClassName}",
                "stepTag": ${step.step},
                "stepData": ${if (step.data != null) "\"${step.data}\"" else "null"},
                "isRunCoroutineIO": ${step.isRunCoroutineIO},
                "repeatCount": ${step.repeatCount},
                "timestamp": ${System.currentTimeMillis()}
            }
        """.trimIndent()
    }
}
