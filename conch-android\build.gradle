apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'com.ven.assists.simple'
    compileSdk rootProject.ext.compileSdk
    defaultConfig {
        applicationId "com.ven.assists.demo"
        minSdk rootProject.ext.minSdk
        targetSdk rootProject.ext.targetSdk
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }

    signingConfigs {
        debug {
            storeFile file('assists_simple_key.jks')
            storePassword '123456'
            keyAlias 'key0'
            keyPassword '123456'
        }
        release {
            storeFile file('assists_simple_key.jks')
            storePassword '123456'
            keyAlias 'key0'
            keyPassword '123456'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            debuggable true
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable false
        }
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "17"
        }
    }
    buildFeatures {
        dataBinding true
    }
    viewBinding {
        enabled = true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':assists')
    implementation project(':assists-mp')
    implementation project(':assists-opcv')
    implementation project(':assists-web')
//    implementation "com.github.ven-coder.Assists:assists-base:v3.2.16"
//    implementation "com.github.ven-coder.Assists:assists-opcv:v3.2.16"
//    implementation "com.github.ven-coder.Assists:assists-mp:v3.2.16"
//    implementation "com.github.ven-coder.Assists:assists-web:v3.2.16"
    implementation "androidx.room:room-runtime:${rootProject.ext.roomVersion}"
    implementation "androidx.room:room-ktx:${rootProject.ext.roomVersion}"
    implementation "com.github.mrmike:ok2curl:${rootProject.ext.ok2curlVersion}"
    implementation "com.lzy.net:okgo:${rootProject.ext.okgoVersion}"
    implementation "com.squareup.okhttp3:logging-interceptor:${rootProject.ext.okhttpLoggingVersion}"
    implementation "androidx.appcompat:appcompat:${rootProject.ext.appcompatVersion}"
    implementation "androidx.constraintlayout:constraintlayout:${rootProject.ext.constraintlayoutVersion}"
    implementation "com.github.li-xiaojun:XPopup:${rootProject.ext.xpopupVersion}"
    implementation "com.google.android.material:material:${rootProject.ext.materialVersion}"
    implementation "androidx.recyclerview:recyclerview:${rootProject.ext.recyclerviewVersion}"
    implementation "com.github.bumptech.glide:glide:${rootProject.ext.glideVersion}"
    annotationProcessor "com.github.bumptech.glide:compiler:${rootProject.ext.glideVersion}"
    implementation "com.google.android.flexbox:flexbox:${rootProject.ext.flexboxVersion}"

    // 网络通信依赖
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'org.java-websocket:Java-WebSocket:1.5.4'
    implementation 'com.google.code.gson:gson:2.10.1'
}
