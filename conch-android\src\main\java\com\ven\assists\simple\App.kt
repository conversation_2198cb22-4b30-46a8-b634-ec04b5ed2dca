package com.ven.assists.simple

import android.app.Application
import com.blankj.utilcode.util.Utils
import com.ven.assists.AssistsCore
import com.ven.assists.simple.network.NetworkManager
import com.ven.assists.simple.network.CommandInterceptor
import com.ven.assists.simple.step.GestureBottomTab
import com.ven.assists.simple.step.GestureScrollSocial
import com.ven.assists.simple.step.OpenWechatSocial
import com.ven.assists.simple.step.PublishSocial
import com.ven.assists.simple.step.ScrollContacts
import com.ven.assists.stepper.StepManager

class App : Application() {

    companion object {
        const val TARGET_PACKAGE_NAME = "com.tencent.mm"
        lateinit var networkManager: NetworkManager
    }

    override fun onCreate() {
        super.onCreate()
        Utils.init(this)
        //设置全局步骤默认间隔时长
        StepManager.DEFAULT_STEP_DELAY = 1000L

        // 初始化网络管理器
        networkManager = NetworkManager.getInstance(this)

        // 注册指令拦截器
        StepManager.stepListeners = CommandInterceptor()
    }
}