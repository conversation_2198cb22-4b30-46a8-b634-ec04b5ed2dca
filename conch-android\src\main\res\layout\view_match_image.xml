<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="查找识别当前屏幕中微信图标位置"
            android:textColor="@color/_ffffff" />

        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="#cccccc" />

        <Button
            android:id="@+id/btn_discern"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开始识别" />
    </LinearLayout>
</FrameLayout>